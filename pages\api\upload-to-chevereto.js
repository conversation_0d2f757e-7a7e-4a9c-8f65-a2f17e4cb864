/**
 * API endpoint para upload de imagens para Chevereto
 *
 * Este endpoint recebe uma URL de imagem e faz upload para o serviço Chevereto
 * configurado, retornando a URL da imagem hospedada.
 */

export default async function handler(req, res) {
  // Apenas aceitar requisições POST
  if (req.method !== 'POST') {
    return res.status(405).json({
      error: 'Método não permitido',
      message: 'Este endpoint aceita apenas requisições POST'
    });
  }

  // Validar se a chave da API está configurada
  if (!process.env.CHEVERETO_API_KEY) {
    console.error('CHEVERETO_API_KEY não está configurada no ambiente');
    return res.status(500).json({
      error: 'Configuração do servidor',
      message: 'Serviço de upload não está configurado corretamente'
    });
  }

  const { imageUrl } = req.body;

  // Validar parâmetros da requisição
  if (!imageUrl) {
    return res.status(400).json({
      error: 'Parâmetro obrigatório',
      message: 'URL da imagem é obrigatória'
    });
  }

  // Validar se é uma URL válida
  try {
    new URL(imageUrl);
  } catch (error) {
    return res.status(400).json({
      error: 'URL inválida',
      message: 'A URL fornecida não é válida'
    });
  }

  // Validar se é uma URL de imagem do Google (por segurança)
  if (!imageUrl.includes('googleapis.com')) {
    return res.status(400).json({
      error: 'URL não permitida',
      message: 'Apenas URLs de imagens do Google Places são permitidas'
    });
  }

  try {
    // Primeiro, fazer download da imagem
    console.log('Fazendo download da imagem:', imageUrl);
    const imageResponse = await fetch(imageUrl);

    if (!imageResponse.ok) {
      console.error('Erro ao fazer download da imagem:', imageResponse.status);
      return res.status(400).json({
        error: 'Erro no download',
        message: 'Não foi possível fazer download da imagem do Google Places'
      });
    }

    // Obter o buffer da imagem
    const imageBuffer = await imageResponse.arrayBuffer();

    // Determinar extensão baseada no content-type
    const contentType = imageResponse.headers.get('content-type') || 'image/jpeg';
    const extension = contentType.includes('png') ? 'png' : 'jpg';
    const filename = `gplaces-img.${extension}`;

    // Criar blob com o tipo correto
    const imageBlob = new Blob([imageBuffer], { type: contentType });

    // Preparar dados para o Chevereto
    const formData = new FormData();
    formData.append('source', imageBlob, filename);
    formData.append('format', 'json');

    console.log('Enviando para Chevereto, tamanho:', imageBuffer.byteLength, 'bytes');

    // Fazer requisição para a API do Chevereto
    const cheveretoResponse = await fetch('https://db.avenca.cloud/api/1/upload', {
      method: 'POST',
      headers: {
        'X-API-Key': process.env.CHEVERETO_API_KEY,
      },
      body: formData,
    });

    // Verificar se a resposta foi bem-sucedida
    if (!cheveretoResponse.ok) {
      const errorText = await cheveretoResponse.text();
      console.error('Erro na API do Chevereto:', cheveretoResponse.status, errorText);

      // Tratar diferentes tipos de erro HTTP
      switch (cheveretoResponse.status) {
        case 401:
          return res.status(500).json({
            error: 'Erro de autenticação',
            message: 'Chave da API inválida ou expirada'
          });
        case 403:
          return res.status(500).json({
            error: 'Acesso negado',
            message: 'Sem permissão para fazer upload'
          });
        case 413:
          return res.status(400).json({
            error: 'Arquivo muito grande',
            message: 'A imagem excede o tamanho máximo permitido'
          });
        case 415:
          return res.status(400).json({
            error: 'Formato não suportado',
            message: 'Formato de imagem não é suportado'
          });
        case 429:
          return res.status(429).json({
            error: 'Muitas requisições',
            message: 'Limite de uploads excedido. Tente novamente em alguns minutos'
          });
        case 503:
          return res.status(503).json({
            error: 'Serviço indisponível',
            message: 'Servidor de upload temporariamente indisponível'
          });
        default:
          return res.status(500).json({
            error: 'Erro no servidor',
            message: 'Erro interno no serviço de upload'
          });
      }
    }

    // Processar resposta JSON
    const cheveretoData = await cheveretoResponse.json();

    // Verificar se o upload foi bem-sucedido
    if (cheveretoData.status_code !== 200 || !cheveretoData.success) {
      console.error('Erro no upload do Chevereto:', cheveretoData);
      return res.status(400).json({
        error: 'Falha no upload',
        message: cheveretoData.error?.message || 'Não foi possível fazer upload da imagem'
      });
    }

    // Extrair URL da imagem uploadada
    const uploadedImageUrl = cheveretoData.image?.url;
    if (!uploadedImageUrl) {
      console.error('URL da imagem não encontrada na resposta:', cheveretoData);
      return res.status(500).json({
        error: 'Resposta inválida',
        message: 'URL da imagem não foi retornada pelo serviço'
      });
    }

    // Retornar sucesso com dados da imagem
    return res.status(200).json({
      success: true,
      message: 'Imagem enviada com sucesso',
      data: {
        url: uploadedImageUrl,
        viewerUrl: cheveretoData.image?.url_viewer,
        filename: cheveretoData.image?.filename,
        size: cheveretoData.image?.size_formatted,
        uploadDate: cheveretoData.image?.date,
      }
    });

  } catch (error) {
    console.error('Erro ao fazer upload para Chevereto:', error);

    // Tratar diferentes tipos de erro
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return res.status(503).json({
        error: 'Erro de conectividade',
        message: 'Não foi possível conectar ao serviço de upload. Tente novamente em alguns instantes'
      });
    }

    if (error.name === 'AbortError') {
      return res.status(408).json({
        error: 'Timeout',
        message: 'Upload demorou muito para completar. Tente novamente'
      });
    }

    return res.status(500).json({
      error: 'Erro interno',
      message: 'Erro inesperado durante o upload. Tente novamente'
    });
  }
}
