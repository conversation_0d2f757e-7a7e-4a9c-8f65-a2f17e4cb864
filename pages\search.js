import Head from 'next/head'
import SearchForm from '../components/searchForm'
import PlaceItem from '../components/placeItem'
import Layout from '../components/layout'

export async function getServerSideProps({ query }) {
  const { q } = query;

  if (!q) {
    return {
      redirect: {
        destination: '/',
        permanent: false,
      },
    }
  }

  try {
    const url = `https://maps.googleapis.com/maps/api/place/findplacefromtext/json?key=${process.env.API_KEY}&inputtype=textquery&input=${encodeURIComponent(q)}&fields=formatted_address,icon,name,photos,place_id,types`
    const res = await fetch(url);
    const resJson = await res.json();
    
    const data = {
      status: resJson.status,
      query: q,
      candidates: resJson.candidates ? resJson.candidates.map(item => {
        let image = ''

        if ('photos' in item && item.photos.length > 0) {
          image = `https://maps.googleapis.com/maps/api/place/photo?key=${process.env.API_KEY}&maxwidth=400&photoreference=${item.photos[0].photo_reference}`
        }

        return {
          formatted_address: item.formatted_address,
          icon: item.icon,
          name: item.name,
          place_id: item.place_id,
          types: item.types || [],
          image: image,
        }
      }) : [],
    }

    return {
      props: {
        data,
      }
    }
  } catch (error) {
    console.error('Error fetching places:', error);
    return {
      props: {
        data: {
          status: 'ERROR',
          query: q,
          candidates: [],
        }
      }
    }
  }
}

export default function SearchPage({ data }) {
  return (
    <Layout>
      <Head>
        <title>{data.query ? `Resultados para "${data.query}"` : 'Buscar Lugares'} - Places Finder</title>
        <meta name="description" content={`Resultados da busca por "${data.query}"`} />
      </Head>

      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Search Form */}
          <div className="mb-8">
            <SearchForm />
          </div>

          {/* Search Results Header */}
          {data.query && (
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Resultados para "{data.query}"
              </h1>
              {data.status === 'OK' && data.candidates.length > 0 && (
                <p className="text-gray-600">
                  Encontramos {data.candidates.length} resultado{data.candidates.length !== 1 ? 's' : ''}
                </p>
              )}
            </div>
          )}

          {/* Results */}
          {data.status === 'OK' && data.candidates.length > 0 && (
            <div className="space-y-6">
              {data.candidates.map((place) => (
                <PlaceItem place={place} key={place.place_id} />
              ))}
            </div>
          )}

          {/* No Results */}
          {data.status === 'ZERO_RESULTS' && (
            <div className="text-center py-12">
              <div className="mb-6">
                <svg className="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Nenhum lugar encontrado
              </h2>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                Não encontramos nenhum resultado para "{data.query}". Tente refinar sua busca ou usar termos diferentes.
              </p>
              <div className="space-y-2 text-sm text-gray-500">
                <p>Dicas para uma busca melhor:</p>
                <ul className="list-disc list-inside space-y-1 max-w-md mx-auto text-left">
                  <li>Use nomes específicos de lugares</li>
                  <li>Inclua a cidade ou país</li>
                  <li>Tente termos em inglês</li>
                  <li>Verifique a ortografia</li>
                </ul>
              </div>
            </div>
          )}

          {/* Error State */}
          {data.status === 'ERROR' && (
            <div className="text-center py-12">
              <div className="mb-6">
                <svg className="mx-auto h-16 w-16 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Erro na busca
              </h2>
              <p className="text-gray-600 mb-6">
                Ocorreu um erro ao buscar por "{data.query}". Tente novamente em alguns instantes.
              </p>
              <button 
                onClick={() => window.location.reload()} 
                className="btn-primary"
              >
                Tentar Novamente
              </button>
            </div>
          )}
        </div>
      </div>
    </Layout>
  )
}
