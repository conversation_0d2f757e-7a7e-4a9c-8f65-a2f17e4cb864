import Head from 'next/head'
import SearchForm from '../components/searchForm'
import Layout from '../components/layout'

export default function HomePage() {
  return (
    <Layout>
      <Head>
        <title>Google Places API</title>
        <meta name="description" content="Descubra lugares incríveis ao redor do mundo com informações detalhadas e fotos." />
      </Head>

      <div className="min-h-screen bg-linear-to-br from-blue-50 to-indigo-100 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="mb-8">
              <span className="text-6xl">🌍</span>
            </div>
            <h1 className="text-5xl font-bold text-gray-900 mb-4">
              Google Places API
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Descubra lugares incríveis, veja fotos detalhadas e obtenha informações completas sobre qualquer local do mundo.
            </p>
          </div>

          <div className="max-w-2xl mx-auto">
            <SearchForm />
          </div>

          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 shadow-lg">
                <span className="text-2xl">🔍</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Busca Inteligente</h3>
              <p className="text-gray-600">
                Use o autocompletar do Google para encontrar qualquer lugar rapidamente.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 shadow-lg">
                <span className="text-2xl">📸</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Fotos em Alta Qualidade</h3>
              <p className="text-gray-600">
                Veja fotos reais dos lugares com opção de download em alta resolução.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 shadow-lg">
                <span className="text-2xl">ℹ️</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Informações Detalhadas</h3>
              <p className="text-gray-600">
                Obtenha endereços completos, tipos de estabelecimento e muito mais.
              </p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
