import Link from 'next/link'
import Head from 'next/head'
import Layout from '../../components/layout'
import PlaceItem from '../../components/placeItem'
import PlacePhoto from '../../components/placePhoto'
import React, { useState, useCallback, useRef, useEffect } from 'react'

export async function getServerSideProps(context) {
  const url = `https://maps.googleapis.com/maps/api/place/details/json?key=${process.env.API_KEY}&place_id=${context.params.id}&fields=formatted_address,icon,name,photos,place_id,types,photos,rating,reviews,formatted_phone_number,website,opening_hours,price_level,international_phone_number,vicinity,url`
  const res = await fetch(url);
  const resJson = await res.json();

  let photos = [];

  if (resJson.status == 'OK') {
    if ('photos' in resJson.result) {
      resJson.result.photos.forEach(photo => {
        photos.push({
          thumbnail: `https://maps.googleapis.com/maps/api/place/photo?key=${process.env.API_KEY}&maxwidth=400&photoreference=${photo.photo_reference}`,
          large: `https://maps.googleapis.com/maps/api/place/photo?key=${process.env.API_KEY}&maxwidth=${photo.width}&maxheight=${photo.height}&photoreference=${photo.photo_reference}`,
          attributions: photo.html_attributions,
        })
      })
    }
  }

  return {
    props: {
      data: {
        status: resJson.status,
        result: resJson.result,
        photos,
      }
    }
  }
}

export default function PlaceDetails({ data }) {
  const [copySuccess, setCopySuccess] = useState('');
  const [reviews, setReviews] = useState(data.result.reviews || []);
  const [isLoadingMoreReviews, setIsLoadingMoreReviews] = useState(false);
  const [reviewsPage, setReviewsPage] = useState(1);
  const [noMoreReviews, setNoMoreReviews] = useState(false);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const debounceTimeoutRef = useRef(null);
  const abortControllerRef = useRef(null);

  // Utility function to validate review data
  const validateReview = (review) => {
    return (
      review &&
      typeof review === 'object' &&
      typeof review.author_name === 'string' &&
      review.author_name.trim() !== '' &&
      typeof review.text === 'string' &&
      review.text.trim() !== '' &&
      typeof review.rating === 'number' &&
      review.rating >= 1 &&
      review.rating <= 5 &&
      typeof review.time === 'number' &&
      review.time > 0
    );
  };

  // Utility function to sort reviews chronologically (newest first)
  const sortReviewsChronologically = (reviewsArray) => {
    return [...reviewsArray].sort((a, b) => b.time - a.time);
  };

  // Utility function to merge and deduplicate reviews
  const mergeReviews = (existingReviews, newReviews) => {
    const validNewReviews = newReviews.filter(validateReview);
    const combined = [...existingReviews, ...validNewReviews];

    // Remove duplicates based on author_name and text combination
    const unique = combined.filter((review, index, self) =>
      index === self.findIndex(r =>
        r.author_name === review.author_name &&
        r.text === review.text &&
        r.time === review.time
      )
    );

    return sortReviewsChronologically(unique);
  };

  const copyToClipboard = (text, field) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        setCopySuccess(`${field} copiado!`);
        setTimeout(() => setCopySuccess(''), 2000);
      })
      .catch(err => {
        console.error('Erro ao copiar texto: ', err);
      });
  };

  const loadMoreReviews = useCallback(async () => {
    // Prevent multiple simultaneous requests
    if (isLoadingMoreReviews || noMoreReviews) return;

    // Clear any existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Debounce implementation
    debounceTimeoutRef.current = setTimeout(async () => {
      setIsLoadingMoreReviews(true);
      setError(null);

      // Create new AbortController for this request
      abortControllerRef.current = new AbortController();

      try {
        const response = await fetch(
          `/api/more-reviews?placeId=${data.result.place_id}&page=${reviewsPage + 1}&limit=5`,
          {
            signal: abortControllerRef.current.signal,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const newData = await response.json();

        // Validate response structure
        if (!newData || typeof newData !== 'object') {
          throw new Error('Invalid response format');
        }

        if (newData.error) {
          throw new Error(newData.error);
        }

        if (newData.reviews && Array.isArray(newData.reviews)) {
          if (newData.reviews.length > 0) {
            // Merge new reviews with existing ones, maintaining chronological order
            const mergedReviews = mergeReviews(reviews, newData.reviews);
            setReviews(mergedReviews);
            setReviewsPage(reviewsPage + 1);
            setRetryCount(0); // Reset retry count on success

            // Check if there are more reviews available from API response
            if (newData.hasMore === false) {
              setNoMoreReviews(true);
            }
          } else {
            setNoMoreReviews(true);
          }
        } else {
          throw new Error('Invalid reviews data structure');
        }

      } catch (error) {
        if (error.name === 'AbortError') {
          // Request was cancelled, don't show error
          return;
        }

        console.error('Erro ao carregar mais reviews:', error);

        // Implement retry logic for network errors
        if (retryCount < 3 && (error.message.includes('fetch') || error.message.includes('network'))) {
          setRetryCount(prev => prev + 1);
          setTimeout(() => {
            loadMoreReviews();
          }, 1000 * Math.pow(2, retryCount)); // Exponential backoff
        } else {
          setError(error.message || 'Erro ao carregar mais avaliações. Tente novamente.');
        }
      } finally {
        setIsLoadingMoreReviews(false);
        abortControllerRef.current = null;
      }
    }, 300); // 300ms debounce delay

  }, [isLoadingMoreReviews, noMoreReviews, data.result.place_id, reviewsPage, reviews, retryCount]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Initialize reviews with proper sorting
  useEffect(() => {
    if (data.result.reviews && data.result.reviews.length > 0) {
      const validReviews = data.result.reviews.filter(validateReview);
      const sortedReviews = sortReviewsChronologically(validReviews);
      setReviews(sortedReviews);
    }
  }, [data.result.reviews]);

  const renderCopyButton = (text, field) => {
    if (!text) return null;

    return (
      <button
        onClick={() => copyToClipboard(text, field)}
        className="ml-2 p-1 bg-gray-100 hover:bg-gray-200 rounded-sm text-gray-600 transition-colors"
        title={`Copiar ${field}`}
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
        </svg>
      </button>
    );
  };

  const renderStars = (rating) => {
    if (!rating) return null;

    const stars = [];
    const fullStars = Math.floor(rating);
    const halfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        );
      } else if (i === fullStars && halfStar) {
        stars.push(
          <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <defs>
              <linearGradient id="half-star" x1="0" x2="100%" y1="0" y2="0">
                <stop offset="50%" stopColor="currentColor" />
                <stop offset="50%" stopColor="#e5e7eb" />
              </linearGradient>
            </defs>
            <path fill="url(#half-star)" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        );
      } else {
        stars.push(
          <svg key={i} className="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        );
      }
    }

    return <div className="flex">{stars}</div>;
  };

  const formatPriceLevel = (priceLevel) => {
    if (priceLevel === undefined) return 'Não informado';

    const levels = {
      0: 'Gratuito',
      1: 'Econômico',
      2: 'Moderado',
      3: 'Caro',
      4: 'Muito caro'
    };

    return levels[priceLevel] || 'Não informado';
  };

  return (
    <Layout>
      <Head>
        <title>{data.result.name}</title>
      </Head>

      <div className="container mx-auto px-4 md:px-10">
        <div className="my-10 md:my-20 w-full lg:w-8/12 mx-auto mb-20">

          {data.status === 'OK' && (
            <React.Fragment>
              <Link href="/" className="text-center px-4 py-2 rounded-md border border-indigo-600 leading-6 font-medium text-sm text-indigo-600 hover:text-white hover:bg-indigo-600">
                &lsaquo; Voltar
              </Link>

              <hr className="my-10" />

              <PlaceItem place={data.result} noButton />

              {copySuccess && (
                <div className="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded-sm shadow-lg">
                  {copySuccess}
                </div>
              )}

              <div className="mt-8 bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Informações Detalhadas</h2>

                {data.result.formatted_phone_number && (
                  <div className="mb-4 flex items-center">
                    <div className="shrink-0 mr-3">
                      <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700">Telefone:</span>
                      <span className="ml-2 text-gray-600">{data.result.formatted_phone_number}</span>
                      {renderCopyButton(data.result.formatted_phone_number, 'Telefone')}
                    </div>
                  </div>
                )}

                {data.result.international_phone_number && (
                  <div className="mb-4 flex items-center">
                    <div className="shrink-0 mr-3">
                      <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700">Telefone Internacional:</span>
                      <span className="ml-2 text-gray-600">{data.result.international_phone_number}</span>
                      {renderCopyButton(data.result.international_phone_number, 'Telefone Internacional')}
                    </div>
                  </div>
                )}

                {data.result.website && (
                  <div className="mb-4 flex items-center">
                    <div className="shrink-0 mr-3">
                      <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                      </svg>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700">Website:</span>
                      <a href={data.result.website} target="_blank" rel="noopener noreferrer" className="ml-2 text-indigo-600 hover:text-indigo-800 truncate max-w-xs">
                        {data.result.website}
                      </a>
                      {renderCopyButton(data.result.website, 'Website')}
                    </div>
                  </div>
                )}

                {data.result.url && (
                  <div className="mb-4 flex items-center">
                    <div className="shrink-0 mr-3">
                      <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700">Google Maps:</span>
                      <a href={data.result.url} target="_blank" rel="noopener noreferrer" className="ml-2 text-indigo-600 hover:text-indigo-800">
                        Ver no Google Maps
                      </a>
                      {renderCopyButton(data.result.url, 'Link do Google Maps')}
                    </div>
                  </div>
                )}

                {data.result.rating && (
                  <div className="mb-4 flex items-center">
                    <div className="shrink-0 mr-3">
                      <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                      </svg>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700">Avaliação:</span>
                      <div className="ml-2 flex items-center">
                        {renderStars(data.result.rating)}
                        <span className="ml-2 text-gray-600">{data.result.rating} / 5</span>
                      </div>
                    </div>
                  </div>
                )}

                {data.result.price_level !== undefined && (
                  <div className="mb-4 flex items-center">
                    <div className="shrink-0 mr-3">
                      <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700">Faixa de Preço:</span>
                      <span className="ml-2 text-gray-600">{formatPriceLevel(data.result.price_level)}</span>
                    </div>
                  </div>
                )}

                {data.result.opening_hours && (
                  <div className="mb-4">
                    <div className="flex items-center mb-2">
                      <div className="shrink-0 mr-3">
                        <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span className="font-medium text-gray-700">Horário de Funcionamento:</span>
                      <span className="ml-2 px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                        {data.result.opening_hours.open_now ? 'Aberto agora' : 'Fechado agora'}
                      </span>
                    </div>

                    {data.result.opening_hours.weekday_text && (
                      <div className="ml-8 mt-2">
                        <div className="bg-gray-50 rounded-md p-3">
                          {data.result.opening_hours.weekday_text.map((day, index) => (
                            <div key={index} className="flex justify-between text-sm py-1">
                              <span className="text-gray-600">{day}</span>
                              {renderCopyButton(day, `Horário ${index + 1}`)}
                            </div>
                          ))}
                          {renderCopyButton(data.result.opening_hours.weekday_text.join('\n'), 'Todos os horários')}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {reviews && reviews.length > 0 && (
                <div className="mt-8 bg-white rounded-lg shadow-md p-6">
                  <h2 className="text-xl font-bold text-gray-900 mb-4">Avaliações</h2>

                  <div className="space-y-6">
                    {reviews.map((review, index) => (
                      <div key={index} className="border-b border-gray-200 pb-6 last:border-b-0 last:pb-0">
                        <div className="flex items-start">
                          <div className="shrink-0">
                            {review.profile_photo_url ? (
                              <img
                                src={review.profile_photo_url}
                                alt={review.author_name}
                                className="h-10 w-10 rounded-full"
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                              </div>
                            )}
                          </div>

                          <div className="ml-4 flex-1">
                            <div className="flex items-center justify-between">
                              <h3 className="text-sm font-medium text-gray-900">{review.author_name}</h3>
                              <div className="flex items-center">
                                <time className="text-xs text-gray-500">
                                  {new Date(review.time * 1000).toLocaleDateString()}
                                </time>
                                {renderCopyButton(`${review.author_name} - ${review.text}`, `Avaliação de ${review.author_name}`)}
                              </div>
                            </div>

                            <div className="mt-1 flex items-center">
                              {renderStars(review.rating)}
                            </div>

                            <div className="mt-2 text-sm text-gray-600 whitespace-pre-line">
                              {review.text}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-8 flex flex-col items-center space-y-4">
                    {/* Error message */}
                    {error && (
                      <div className="w-full max-w-md bg-red-50 border border-red-200 rounded-md p-4">
                        <div className="flex items-center">
                          <svg className="h-5 w-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          <p className="text-sm text-red-700">{error}</p>
                        </div>
                        <button
                          onClick={() => {
                            setError(null);
                            setRetryCount(0);
                          }}
                          className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
                        >
                          Tentar novamente
                        </button>
                      </div>
                    )}

                    {/* Load more button */}
                    {!noMoreReviews && !error && (
                      <button
                        onClick={loadMoreReviews}
                        disabled={isLoadingMoreReviews}
                        className={`px-6 py-3 rounded-md border border-indigo-600 font-medium text-sm transition-all duration-200 ${isLoadingMoreReviews
                          ? 'bg-gray-100 text-gray-500 cursor-not-allowed border-gray-300'
                          : 'text-indigo-600 hover:text-white hover:bg-indigo-600 hover:shadow-md transform hover:scale-105'
                          }`}
                      >
                        {isLoadingMoreReviews ? (
                          <span className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Carregando avaliações...
                            {retryCount > 0 && (
                              <span className="ml-1 text-xs">
                                (tentativa {retryCount + 1})
                              </span>
                            )}
                          </span>
                        ) : (
                          <span className="flex items-center">
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                            </svg>
                            Carregar mais avaliações
                          </span>
                        )}
                      </button>
                    )}

                    {/* No more reviews message */}
                    {noMoreReviews && !error && (
                      <div className="text-center">
                        <svg className="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p className="text-gray-500 text-sm">
                          Todas as avaliações foram carregadas.
                        </p>
                      </div>
                    )}

                    {/* Loading skeleton for better UX */}
                    {isLoadingMoreReviews && (
                      <div className="w-full space-y-4 animate-pulse">
                        {[1, 2, 3].map((i) => (
                          <div key={i} className="border-b border-gray-200 pb-4">
                            <div className="flex items-start space-x-4">
                              <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
                              <div className="flex-1 space-y-2">
                                <div className="h-4 bg-gray-200 rounded-sm w-1/4"></div>
                                <div className="h-3 bg-gray-200 rounded-sm w-1/6"></div>
                                <div className="space-y-1">
                                  <div className="h-3 bg-gray-200 rounded-sm"></div>
                                  <div className="h-3 bg-gray-200 rounded-sm w-5/6"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}

              <hr className="my-10" />

              <h2 className="text-xl font-bold text-gray-900 mb-4">Fotos</h2>
              <div className="flex -mx-2 flex-wrap">
                {data.photos.map((photo, i) => (
                  <div className="w-6/12 md:w-4/12 mb-4 px-2" key={i}>
                    <PlacePhoto photo={photo} />
                  </div>
                ))}
              </div>
            </React.Fragment>
          )}

        </div>
      </div>
    </Layout>
  )
}
